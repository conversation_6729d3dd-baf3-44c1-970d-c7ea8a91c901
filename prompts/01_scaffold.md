+++SQ

Scaffold a new node.js typescript project with the following technical requirements:

- Should use typescript
- Source code should be in src/
- Build output should be in dist/
- Use yarn for the package manager
- tsconfig.json should extend the following packages in order:
  - @tsconfig/node22
  - @tsconfig/strictest
- Add the latest version of typescript eslint
- Add prettier
- Do not use prettier to format typescript source code
- Use eslint for both linting and formatting typescript source code
- Set `noUnusedLocals` to false in tsconfig.json
- Use eslint to warn about unused variables and imports
- Do not add any testing libraries
- Add a simple src/index.ts file the prints hello world to the console
